import type { Metada<PERSON> } from "next";
import Header from "@/components/ui/Header";
import "./globals.css";

export const metadata: Metadata = {
  title: "<PERSON><PERSON> | Cloud Security Engineer & Web Developer",
  description: "Portfolio of <PERSON><PERSON> - Cloud Engineering, Cybersecurity & Web Development Professional specializing in AWS, Azure, React, and security solutions.",
  keywords: "<PERSON><PERSON>, Cloud Engineer, Cybersecurity, Web Developer, AWS, Azure, React, Next.js, Dev<PERSON><PERSON>, Security Engineer",
  authors: [{ name: "<PERSON><PERSON>" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="font-sans antialiased bg-background-dark text-text-primary grid-pattern">
        <Header />
        <div className="min-h-screen relative">
          {children}
        </div>
      </body>
    </html>
  );
}
