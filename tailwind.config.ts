import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom cybersecurity + cloud theme colors
        primary: {
          blue: "#00D4FF",    // Electric Blue - cloud elements
          green: "#00FF88",   // Neon Green - active/success states
          orange: "#FF6B35",  // Orange - highlights/alerts
          purple: "#8B5CF6",  // Purple - premium features
        },
        background: {
          dark: "#0A0A0F",    // Deep dark background
          navy: "#1A1B2E",    // Navy background
          card: "#16213E",    // Card backgrounds
        },
        text: {
          primary: "#FFFFFF",   // Primary text
          secondary: "#B0B3B8", // Secondary text
          accent: "#00D4FF",    // Accent text
        },
        border: {
          glow: "#00D4FF",     // Glowing borders
          subtle: "#2A2D3A",   // Subtle borders
        }
      },
      fontFamily: {
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', 'monospace'],
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
        'scan-line': 'scan-line 3s linear infinite',
        'data-flow': 'data-flow 4s linear infinite',
      },
      keyframes: {
        'pulse-glow': {
          '0%': { boxShadow: '0 0 20px #00D4FF' },
          '100%': { boxShadow: '0 0 40px #00FF88, 0 0 60px #00D4FF' },
        },
        'scan-line': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'data-flow': {
          '0%': { backgroundPosition: '0% 0%' },
          '100%': { backgroundPosition: '100% 100%' },
        },
      },
    },
  },
  plugins: [],
};
export default config;
