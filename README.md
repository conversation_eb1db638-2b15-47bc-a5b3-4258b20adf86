# 🚀 <PERSON><PERSON> - Portfolio Website

A modern, interactive portfolio website showcasing skills in Cloud Engineering, Cybersecurity, and Web Development.

## ✨ Features

- **Interactive Terminal** - Command-line interface for navigation
- **Responsive Design** - Works on all devices
- **Modern Animations** - Smooth transitions and hover effects
- **Professional Sections** - About, Skills, Projects, Experience, Contact
- **Clean Architecture** - Built with Next.js 15 and TypeScript

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom React components
- **Animations**: CSS transitions and transforms

## 🚀 Quick Start

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Run development server**:

   ```bash
   npm run dev
   ```

3. **Open browser**: Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
ansharma/
├── src/
│   ├── app/
│   │   ├── page.tsx          # Main portfolio page
│   │   ├── layout.tsx        # Root layout
│   │   ├── globals.css       # Global styles
│   │   └── favicon.ico       # Site icon
│   └── components/
│       └── ui/
│           └── Avatar.tsx    # Avatar component
├── public/                   # Static assets
├── package.json             # Dependencies
└── README.md               # This file
```

## 🌐 Deployment

Ready for deployment on:

- **Vercel** (recommended)
- **Netlify**
- **GitHub Pages**
- Any static hosting service

## 📧 Contact

**Ansh Sharma**

- Email: <EMAIL>
- GitHub: [anxious2004](https://github.com/anxious2004)
- Twitter: [@XllAnsh](https://x.com/XllAnsh)

---

Built with ❤️ using Next.js and TypeScript
