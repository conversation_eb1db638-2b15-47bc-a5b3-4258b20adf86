'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import Avatar from '@/components/ui/Avatar';

export default function Home() {
  const [nameText, setNameText] = useState('');
  const [titleText, setTitleText] = useState('');
  const [showCursor, setShowCursor] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const [isVisible, setIsVisible] = useState<{[key: string]: boolean}>({});
  const [isMounted, setIsMounted] = useState(false);
  const heroRef = useRef<HTMLElement>(null);

  // Terminal Command System
  const [terminalInput, setTerminalInput] = useState('');
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    '🚀 Welcome to <PERSON><PERSON>\'s Interactive Portfolio Terminal!',
    '💻 System initialized successfully.',
    '📡 Connection established to portfolio server.',
    '',
    '💡 Type "!help" to see available commands.',
    '🎯 Try "!whoami" to get started!',
    ''
  ]);
  const [currentSection, setCurrentSection] = useState('home');
  const [showTerminal, setShowTerminal] = useState(false);
  const terminalRef = useRef<HTMLDivElement>(null);

  // Memoize static values to prevent re-creation
  const fullName = useMemo(() => "Ansh Sharma", []);
  const fullTitle = useMemo(() => "Cloud Engineer, Cyber Security Analyst & Web Developer", []);

  // Dynamic resume data extracted from website
  // 🔄 UPDATE THIS WHEN YOU CHANGE WEBSITE CONTENT
  const resumeData = {
    name: "Ansh Sharma",
    title: "Cloud Engineer, Cyber Security Analyst & Web Developer",
    email: "<EMAIL>",
    github: "github.com/anxious2004",
    twitter: "@XllAnsh",
    instagram: "@ansharmaap2004",

    education: {
      degree: "B.Tech in Computer Science",
      institution: "Bharati Vidyapeeth's College of Engineering",
      duration: "2022 - 2026",
      cgpa: "9.396"
    },

    experience: [
      {
        title: "IT Intern",
        company: "Indian Aviation Academy",
        duration: "2024 - Present",
        description: "Developing comprehensive feedback management system for trainee evaluations and institutional reporting. Working with modern web technologies to create scalable solutions."
      }
    ],

    skills: {
      cloud: ["AWS (Advanced)", "Azure (Basic)"],
      security: ["Linux", "Wireshark", "Bash Scripting", "Network Security"],
      development: ["React (Intermediate)", "Next.js (Intermediate)", "JavaScript", "TypeScript", "Full-stack Development"],
      tools: ["Git", "DevOps (Intermediate)", "NPM", "Database Management"]
    },

    projects: [
      {
        name: "SafeSearch",
        description: "Web Browsing Security Framework - A comprehensive security framework designed to protect users during web browsing by detecting and preventing malicious activities in real-time.",
        status: "In Development",
        tech: ["Security", "Web Framework", "Real-time Protection"]
      },
      {
        name: "Snacx",
        description: "Social Media Platform - A modern social media platform specifically designed for sharing and discovering memes, featuring user interactions, content curation, and community engagement.",
        status: "Active Development",
        tech: ["Social Media", "Full-Stack", "Community"]
      },
      {
        name: "Trainee Feedback System",
        description: "Enterprise Management System - A comprehensive feedback management system for Indian Aviation Academy, streamlining trainee evaluations, progress tracking, and institutional reporting.",
        status: "In Production",
        tech: ["Enterprise", "Management", "Database"]
      },
      {
        name: "Multiport Security Scanner",
        description: "Network Security Tool - An advanced network scanning and security assessment tool capable of multi-port analysis, vulnerability detection, and comprehensive network mapping.",
        status: "Testing Phase",
        tech: ["Network Security", "Port Scanning", "Vulnerability Assessment"]
      }
    ]
  };

  // Helper function to generate and download dynamic resume
  const downloadResume = () => {
    try {
      // Create dynamic resume HTML
      const resumeHTML = generateResumeHTML(resumeData);

      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(resumeHTML);
        printWindow.document.close();

        // Wait for content to load, then print
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            printWindow.close();
          }, 500);
        };
      } else {
        // Fallback: create downloadable HTML file
        const blob = new Blob([resumeHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'Ansh_Sharma_Resume.html';
        link.click();
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Resume generation failed:', error);
      // Fallback: show resume data in alert
      alert('Resume data:\n\n' + JSON.stringify(resumeData, null, 2));
    }
  };

  // Function to generate resume HTML
  const generateResumeHTML = (data: typeof resumeData) => {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.name} - Resume</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2563eb; padding-bottom: 20px; }
        .header h1 { font-size: 2.5em; color: #2563eb; margin-bottom: 5px; }
        .header h2 { font-size: 1.2em; color: #666; margin-bottom: 10px; }
        .contact { font-size: 0.9em; color: #555; }
        .section { margin-bottom: 25px; }
        .section h3 {
            font-size: 1.3em;
            color: #2563eb;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .education, .experience { margin-bottom: 15px; }
        .education h4, .experience h4 { color: #333; margin-bottom: 5px; }
        .education p, .experience p { color: #666; font-size: 0.9em; }
        .skills-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; }
        .skill-category h4 { color: #2563eb; margin-bottom: 8px; }
        .skill-category ul { list-style: none; }
        .skill-category li {
            background: #f0f9ff;
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .project { margin-bottom: 15px; padding: 10px; border-left: 3px solid #2563eb; background: #f9fafb; }
        .project h4 { color: #2563eb; margin-bottom: 5px; }
        .project p { font-size: 0.9em; margin-bottom: 5px; }
        .project .tech { font-size: 0.8em; color: #666; font-style: italic; }
        .status {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        @media print {
            body { padding: 0; }
            .header { page-break-after: avoid; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${data.name}</h1>
        <h2>${data.title}</h2>
        <div class="contact">
            📧 ${data.email} | 🔗 ${data.github} | 🐦 ${data.twitter} | 📸 ${data.instagram}
        </div>
    </div>

    <div class="section">
        <h3>🎓 Education</h3>
        <div class="education">
            <h4>${data.education.degree}</h4>
            <p><strong>${data.education.institution}</strong> | ${data.education.duration}</p>
            <p>CGPA: <strong>${data.education.cgpa}</strong></p>
        </div>
    </div>

    <div class="section">
        <h3>💼 Experience</h3>
        ${data.experience.map(exp => `
            <div class="experience">
                <h4>${exp.title} <span class="status">${exp.duration}</span></h4>
                <p><strong>${exp.company}</strong></p>
                <p>${exp.description}</p>
            </div>
        `).join('')}
    </div>

    <div class="section">
        <h3>🛠️ Technical Skills</h3>
        <div class="skills-grid">
            <div class="skill-category">
                <h4>☁️ Cloud & Infrastructure</h4>
                <ul>${data.skills.cloud.map(skill => `<li>${skill}</li>`).join('')}</ul>
            </div>
            <div class="skill-category">
                <h4>🛡️ Security & Tools</h4>
                <ul>${data.skills.security.map(skill => `<li>${skill}</li>`).join('')}</ul>
            </div>
            <div class="skill-category">
                <h4>💻 Development</h4>
                <ul>${data.skills.development.map(skill => `<li>${skill}</li>`).join('')}</ul>
            </div>
            <div class="skill-category">
                <h4>🔧 Tools & Others</h4>
                <ul>${data.skills.tools.map(skill => `<li>${skill}</li>`).join('')}</ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h3>🚀 Featured Projects</h3>
        ${data.projects.map(project => `
            <div class="project">
                <h4>${project.name} <span class="status">${project.status}</span></h4>
                <p>${project.description}</p>
                <p class="tech">Technologies: ${project.tech.join(', ')}</p>
            </div>
        `).join('')}
    </div>

    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 0.9em;">
        Generated dynamically from portfolio website • ${new Date().toLocaleDateString()}
    </div>
</body>
</html>`;
  };

  // Terminal Commands Configuration
  const commands = {
    '!help': {
      description: 'Show all available commands',
      usage: '!help',
      action: () => [
        '📋 Available Commands:',
        '',
        '🔍 Navigation:',
        '  !about      - Navigate to About section',
        '  !skills     - Show Skills section',
        '  !projects   - Display Projects',
        '  !experience - View Experience timeline',
        '  !contact    - Get contact information',
        '',
        '💻 System:',
        '  !whoami     - Show brief introduction',
        '  !ls         - List all sections',
        '  !pwd        - Show current section',
        '  !cd <section> - Navigate to section',
        '  !cat <section> - Display section content',
        '  !clear      - Clear terminal output',
        '  !date       - Show current date and time',
        '  !version    - Show portfolio version',
        '  !resume     - View resume summary',
        '  !github     - Get GitHub profile info',
        '  !status     - Check availability status',
        '',
        '🎯 Examples:',
        '  !cd skills    - Navigate to skills section',
        '  !cat projects - Display project details',
        '  !resume       - View resume summary',
        '  !status       - Check availability',
        '',
        'Type any command to explore my portfolio! 💼'
      ]
    },
    '!whoami': {
      description: 'Show brief introduction',
      usage: '!whoami',
      action: () => [
        '👨‍💻 Ansh Sharma',
        '🎓 B.Tech Computer Science Student (CGPA: 9.396)',
        '💼 IT Intern at Indian Aviation Academy',
        '🔐 Cybersecurity Enthusiast & Cloud Developer',
        '🌟 Building secure, scalable solutions',
        '',
        'Specializing in: AWS, Azure, React, Python, Cybersecurity'
      ]
    },
    '!ls': {
      description: 'List all sections',
      usage: '!ls',
      action: () => [
        '📁 Portfolio Sections:',
        '',
        '  home/       - Hero section with introduction',
        '  about/      - Personal story and background',
        '  skills/     - Technical skills and expertise',
        '  projects/   - Featured projects and work',
        '  experience/ - Professional timeline',
        '  contact/    - Get in touch information',
        '',
        'Use "!cd <section>" to navigate or "!cat <section>" to view content'
      ]
    },
    '!pwd': {
      description: 'Show current section',
      usage: '!pwd',
      action: () => [`📍 Current section: /${currentSection}`]
    },
    '!clear': {
      description: 'Clear terminal output',
      usage: '!clear',
      action: () => {
        setTerminalOutput(['✨ Terminal cleared. Type "!help" for commands.', '']);
        return [];
      }
    },
    '!date': {
      description: 'Show current date and time',
      usage: '!date',
      action: () => [`📅 ${new Date().toLocaleString()}`]
    },
    '!version': {
      description: 'Show portfolio version',
      usage: '!version',
      action: () => [
        '📦 Portfolio Version 2.0.0',
        '🚀 Built with Next.js 15.4.4 & React 19.1.0',
        '🎨 Enhanced with interactive terminal commands',
        '✨ Featuring advanced animations & effects'
      ]
    },
    '!resume': {
      description: 'View resume summary and download',
      usage: '!resume',
      action: () => {
        // Trigger resume download
        downloadResume();

        return [
          `📄 Resume Summary - ${resumeData.name}`,
          '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━',
          `🎓 ${resumeData.education.degree} | CGPA: ${resumeData.education.cgpa}`,
          `💼 ${resumeData.experience[0].title} at ${resumeData.experience[0].company}`,
          `☁️ ${resumeData.title}`,
          '',
          '🛠️ Technical Skills:',
          `  • Cloud: ${resumeData.skills.cloud.join(', ')}`,
          `  • Security: ${resumeData.skills.security.join(', ')}`,
          `  • Development: ${resumeData.skills.development.slice(0, 3).join(', ')}`,
          `  • Tools: ${resumeData.skills.tools.join(', ')}`,
          '',
          '🚀 Key Projects:',
          ...resumeData.projects.map(project => `  • ${project.name} - ${project.status}`),
          '',
          `📧 Contact: ${resumeData.email}`,
          `🔗 GitHub: ${resumeData.github}`,
          '',
          '✅ Dynamic resume generated and ready for download!',
          '📁 Resume will open in print dialog or download as HTML'
        ];
      }
    },
    '!matrix': {
      description: 'Enter the Matrix (Easter egg)',
      usage: '!matrix',
      action: () => [
        '🔴 Taking the red pill...',
        '💊 Welcome to the Matrix, Neo.',
        '🌐 Reality is just code.',
        '01001000 01100101 01101100 01101100 01101111',
        '🕶️  "There is no spoon" - The Matrix'
      ]
    },
    '!coffee': {
      description: 'Get some virtual coffee',
      usage: '!coffee',
      action: () => [
        '☕ Brewing virtual coffee...',
        '🫖 *coffee brewing sounds*',
        '☕ Here\'s your coffee! Perfect for coding sessions.',
        '💡 Fun fact: I run on coffee and curiosity!'
      ]
    },
    '!joke': {
      description: 'Get a programming joke',
      usage: '!joke',
      action: () => {
        const jokes = [
          ['🤖 Why do programmers prefer dark mode?', 'Because light attracts bugs! 🐛'],
          ['💻 How many programmers does it take to change a light bulb?', 'None. That\'s a hardware problem! 💡'],
          ['🔧 Why do Java developers wear glasses?', 'Because they can\'t C# ! 👓'],
          ['🐍 Why is Python so popular?', 'Because it\'s not just a snake, it\'s a lifestyle! 🐍'],
          ['☁️ Why did the developer go broke?', 'Because they used up all their cache! 💰']
        ];
        const randomJoke = jokes[Math.floor(Math.random() * jokes.length)];
        return ['😄 Here\'s a programming joke for you:', '', ...randomJoke, ''];
      }
    }
  };

  // Navigation commands
  const navigationCommands = ['about', 'skills', 'projects', 'experience', 'contact', 'home'];

  // Process terminal commands
  const processCommand = (input: string) => {
    const trimmedInput = input.trim().toLowerCase();
    const [command, ...args] = trimmedInput.split(' ');

    // Handle navigation commands (!about, !skills, etc.)
    if (command.startsWith('!') && navigationCommands.includes(command.slice(1))) {
      const section = command.slice(1);
      setCurrentSection(section);
      document.getElementById(section)?.scrollIntoView({ behavior: 'smooth' });
      return [`🧭 Navigating to ${section} section...`, ''];
    }

    // Handle cd command
    if (command === '!cd') {
      const section = args[0];
      if (!section) {
        return ['❌ Usage: !cd <section>', 'Available sections: home, about, skills, projects, experience, contact'];
      }
      if (navigationCommands.includes(section)) {
        setCurrentSection(section);
        document.getElementById(section)?.scrollIntoView({ behavior: 'smooth' });
        return [`🧭 Changed directory to /${section}`, ''];
      }
      return [`❌ Section '${section}' not found. Use "!ls" to see available sections.`];
    }

    // Handle cat command
    if (command === '!cat') {
      const section = args[0];
      if (!section) {
        return ['❌ Usage: !cat <section>', 'Available sections: home, about, skills, projects, experience, contact'];
      }
      return getCatContent(section);
    }

    // Handle predefined commands
    if (commands[command as keyof typeof commands]) {
      return commands[command as keyof typeof commands].action();
    }

    // Unknown command
    return [
      `❌ Command '${command}' not found.`,
      'Type "!help" to see available commands.',
      ''
    ];
  };

  // Get content for cat command
  const getCatContent = (section: string) => {
    const content: {[key: string]: string[]} = {
      home: [
        '🏠 Home Section',
        '==================',
        'Welcome to my interactive portfolio!',
        'I\'m Ansh Sharma, a passionate developer specializing in',
        'cybersecurity and cloud technologies.',
        ''
      ],
      about: [
        '👨‍💻 About Me',
        '=============',
        '🎓 B.Tech Computer Science (2022-2026)',
        '📊 CGPA: 9.396 | Class X: 97% | Class XII: 97.2%',
        '💼 IT Intern at Indian Aviation Academy',
        '🔐 Cybersecurity enthusiast with cloud expertise',
        '📚 Beyond coding: Fantasy novels and continuous learning',
        ''
      ],
      skills: [
        '💻 Technical Skills',
        '==================',
        '🔵 Programming: C++, Java, Python, JavaScript, Rust',
        '🌐 Web Dev: HTML, CSS, React, Node.js, Express, Django, Flask',
        '☁️  Cloud: AWS (EC2, S3, Lambda), Azure, Docker, CI/CD',
        '🗄️  Databases: MySQL, MongoDB',
        '🛡️  Security: Linux, Wireshark, Bash Scripting, NPM',
        '🔧 Tools: Git, Version Control',
        ''
      ],
      projects: [
        '🚀 Featured Projects',
        '===================',
        '🛡️  SafeSearch - Web browsing security framework',
        '😄 Snacx - Social media platform for memes',
        '📋 Trainee Feedback System - Enterprise management (IAA)',
        '⚠️  Hostile JS Playground - Malware analysis tool',
        '🔍 Multiport Security Scanner - Network security tool',
        ''
      ],
      experience: [
        '📈 Professional Journey',
        '======================',
        '💼 2024-Present: IT Intern at Indian Aviation Academy',
        '🎓 2022-2026: B.Tech Computer Science (In Progress)',
        '📚 2020-2022: Academic Foundation (97%+ scores)',
        '🚀 2024-2026: Professional Development & Certifications',
        ''
      ],
      contact: [
        '📞 Contact Information',
        '=====================',
        '📧 Email: <EMAIL>',
        '🔗 GitHub: https://github.com/anxious2004',
        '🐦 Twitter: https://x.com/XllAnsh?t=nGdjvxWitsi0BeSp7BbXfg&s=09',
        '📷 Instagram: https://www.instagram.com/ansharmaap2004/',
        '🚀 Status: Open to opportunities',
        '💡 Interested in: Cybersecurity, Full-stack, Cloud, DevOps',
        ''
      ]
    };

    return content[section] || [`❌ Section '${section}' not found. Use "!ls" to see available sections.`];
  };

  // Mount effect to prevent hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    // Typewriter effect for name
    let nameIndex = 0;
    const nameTimer = setInterval(() => {
      if (nameIndex < fullName.length) {
        setNameText(fullName.slice(0, nameIndex + 1));
        nameIndex++;
      } else {
        clearInterval(nameTimer);
        // Start title animation after name is complete
        setTimeout(() => {
          let titleIndex = 0;
          const titleTimer = setInterval(() => {
            if (titleIndex < fullTitle.length) {
              setTitleText(fullTitle.slice(0, titleIndex + 1));
              titleIndex++;
            } else {
              clearInterval(titleTimer);
              setShowCursor(false);
            }
          }, 50);
        }, 500); // Small delay before starting title animation
      }
    }, 100);

    // Fallback: Ensure title shows after 5 seconds if animation fails
    const fallbackTimer = setTimeout(() => {
      if (!titleText && nameText === fullName) {
        setTitleText(fullTitle);
        setShowCursor(false);
      }
    }, 5000);

    return () => {
      clearInterval(nameTimer);
      clearTimeout(fallbackTimer);
    };
  }, [isMounted, titleText, nameText, fullName, fullTitle]);

  // Real-time clock
  useEffect(() => {
    if (!isMounted) return;

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, [isMounted]);

  // Optimized mouse tracking with throttling
  const handleMouseMove = useCallback((e: MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  }, []);

  // Optimized scroll handler with throttling
  const handleScroll = useCallback(() => {
    setScrollY(window.scrollY);

    // Intersection Observer for scroll animations (less frequent updates)
    const sections = document.querySelectorAll('section[id]');
    sections.forEach((section) => {
      const rect = section.getBoundingClientRect();
      const isInView = rect.top < window.innerHeight * 0.8 && rect.bottom > 0;
      setIsVisible(prev => {
        if (prev[section.id] !== isInView) {
          return { ...prev, [section.id]: isInView };
        }
        return prev;
      });
    });
  }, []);

  // Mouse tracking and scroll effects
  useEffect(() => {
    if (!isMounted) return;

    // More aggressive throttling to prevent hanging
    let mouseTimeout: NodeJS.Timeout;
    const throttledMouseMove = (e: MouseEvent) => {
      if (mouseTimeout) return;
      mouseTimeout = setTimeout(() => {
        handleMouseMove(e);
        mouseTimeout = null as any;
      }, 50); // Reduced to 20fps for better performance
    };

    // More aggressive scroll throttling
    let scrollTimeout: NodeJS.Timeout;
    const throttledScroll = () => {
      if (scrollTimeout) return;
      scrollTimeout = setTimeout(() => {
        handleScroll();
        scrollTimeout = null as any;
      }, 100); // Reduced to 10fps for scroll events
    };

    window.addEventListener('mousemove', throttledMouseMove, { passive: true });
    window.addEventListener('scroll', throttledScroll, { passive: true });

    // Initial check
    handleScroll();

    return () => {
      window.removeEventListener('mousemove', throttledMouseMove);
      window.removeEventListener('scroll', throttledScroll);
      if (mouseTimeout) clearTimeout(mouseTimeout);
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, [isMounted, handleMouseMove, handleScroll]);

  // Handle terminal input
  const handleTerminalSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!terminalInput.trim()) return;

    const output = processCommand(terminalInput);
    setTerminalOutput(prev => [
      ...prev,
      `$ ${terminalInput}`,
      ...output,
      ''
    ]);
    setTerminalInput('');

    // Scroll terminal to bottom
    setTimeout(() => {
      if (terminalRef.current) {
        terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
      }
    }, 100);
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    if (!isMounted) return;

    const handleKeyPress = (e: KeyboardEvent) => {
      // Focus terminal input when typing commands
      if (e.key === '!' && !showTerminal) {
        setShowTerminal(true);
        setTerminalInput('!');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [showTerminal, isMounted]);

  // Prevent hydration issues by not rendering until mounted
  if (!isMounted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-dark">
        <div className="text-primary-blue text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col relative overflow-hidden">
      {/* Custom cursor effect */}
      <div
        className="fixed w-4 h-4 bg-primary-green/30 rounded-full pointer-events-none z-50 transition-all duration-100 ease-out"
        style={{
          left: mousePosition.x - 8,
          top: mousePosition.y - 8,
          transform: `scale(1.1)`,
          opacity: mousePosition.x > 0 ? 0.6 : 0
        }}
      ></div>
      <div
        className="fixed w-8 h-8 border border-primary-blue/20 rounded-full pointer-events-none z-40 transition-all duration-200 ease-out"
        style={{
          left: mousePosition.x - 16,
          top: mousePosition.y - 16,
          opacity: mousePosition.x > 0 ? 0.4 : 0
        }}
      ></div>

      {/* Interactive Terminal */}
      <div className={`fixed bottom-4 right-4 z-50 transition-all duration-500 ${showTerminal ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'}`}>
        <div className="bg-background-dark/95 border border-border-glow rounded-lg shadow-2xl backdrop-blur-md w-96 h-80">
          {/* Terminal Header */}
          <div className="flex items-center justify-between p-3 border-b border-border-subtle">
            <div className="flex items-center gap-2">
              <div className="flex gap-1">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <span className="text-text-secondary text-sm font-mono">portfolio-terminal</span>
            </div>
            <button
              onClick={() => setShowTerminal(false)}
              className="text-text-secondary hover:text-text-primary transition-colors"
            >
              ✕
            </button>
          </div>

          {/* Terminal Output */}
          <div
            ref={terminalRef}
            className="p-3 h-52 overflow-y-auto font-mono text-sm text-text-primary bg-background-dark/50"
          >
            {terminalOutput.map((line, index) => (
              <div key={index} className={`${line.startsWith('$') ? 'text-primary-green' : line.startsWith('❌') ? 'text-red-400' : line.startsWith('🧭') ? 'text-primary-blue' : 'text-text-primary'}`}>
                {line}
              </div>
            ))}
          </div>

          {/* Terminal Input */}
          <form onSubmit={handleTerminalSubmit} className="p-3 border-t border-border-subtle">
            <div className="flex items-center gap-2">
              <span className="text-primary-green font-mono">$</span>
              <input
                type="text"
                value={terminalInput}
                onChange={(e) => setTerminalInput(e.target.value)}
                className="flex-1 bg-transparent text-text-primary font-mono text-sm outline-none"
                placeholder="Type !help for commands..."
                autoFocus={showTerminal}
              />
            </div>
          </form>
        </div>
      </div>

      {/* Terminal Toggle Button */}
      <button
        onClick={() => setShowTerminal(prev => !prev)}
        className={`fixed bottom-4 right-4 z-40 w-12 h-12 bg-primary-green/20 border border-primary-green/30 rounded-full flex items-center justify-center text-primary-green hover:bg-primary-green/30 hover:scale-110 transition-all duration-300 ${showTerminal ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
        title="Open Terminal"
      >
        <span className="text-lg">💻</span>
      </button>

      {/* Keyboard Shortcut Hint */}
      <div className="fixed bottom-20 right-4 z-30 text-xs text-text-secondary font-mono bg-background-card/80 px-2 py-1 rounded border border-border-subtle backdrop-blur-sm">
        Press <kbd className="bg-primary-green/20 px-1 rounded">!</kbd> for terminal
      </div>

      {/* Home Section */}
      <section ref={heroRef} id="home" className="min-h-screen flex flex-col relative overflow-hidden pt-16">
      {/* Enhanced background elements */}
      <div className="absolute inset-0 opacity-10 overflow-hidden">
        {/* Simplified floating particles - no mouse interaction */}
        {[...Array(6)].map((_, i) => (
          <div
            key={`particle-${i}`}
            className={`absolute rounded-full animate-pulse ${
              i % 3 === 0 ? 'w-2 h-2 bg-primary-blue/30' :
              i % 3 === 1 ? 'w-1.5 h-1.5 bg-primary-green/30' :
              'w-1.5 h-1.5 bg-primary-purple/30'
            }`}
            style={{
              left: `${10 + (i * 13) % 80}%`,
              top: `${15 + (i * 11) % 70}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + (i % 2)}s`
            }}
          />
        ))}



        {/* Simplified static gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-48 h-48 bg-primary-blue/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-32 h-32 bg-primary-green/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex items-center justify-center relative z-10 py-20">
        <div className="text-center space-y-12 px-4 max-w-5xl mx-auto">
          {/* Enhanced Terminal-style header with avatar */}
          <div
            className="font-mono text-primary-blue border border-border-glow p-8 rounded-xl bg-background-card/50 backdrop-blur-sm shadow-2xl hover:shadow-primary-blue/30 hover:shadow-2xl hover:scale-105 transition-all duration-500 subtle-glow group hover-glow"
          >
            <div className="text-sm text-text-secondary mb-6 flex items-center justify-center gap-2 group-hover:text-primary-green transition-colors duration-300">
              <span className="w-2 h-2 bg-primary-green rounded-full animate-pulse group-hover:animate-ping"></span>
              <span className="group-hover:animate-pulse">$ whoami</span>
            </div>

            {/* Interactive Terminal Hint with Visual Elements */}
            <div className="relative mb-6">
              <div className="text-xs text-text-secondary/70 mb-4 text-center font-mono">
                💡 Try typing <kbd className="bg-primary-green/20 px-1 rounded text-primary-green">!</kbd> to open terminal or <kbd className="bg-primary-blue/20 px-1 rounded text-primary-blue">!help</kbd> for commands
              </div>

              {/* Animated Tech Elements in Empty Space */}
              <div className="absolute inset-0 pointer-events-none overflow-hidden h-20">
                {/* Floating Code Snippets */}
                <div className="absolute top-2 right-4 text-xs text-primary-green/30 font-mono animate-float opacity-0 animate-fade-in-delayed">
                  <div className="animate-pulse delay-100">{'{'}</div>
                  <div className="ml-2 animate-pulse delay-300">"status": "online",</div>
                  <div className="ml-2 animate-pulse delay-500">"skills": ["React", "AWS"]</div>
                  <div className="animate-pulse delay-700">{'}'}</div>
                </div>

                {/* Binary Rain Effect */}
                <div className="absolute top-1 left-8 text-xs text-primary-blue/20 font-mono">
                  <div className="animate-pulse delay-200">01001000</div>
                  <div className="animate-pulse delay-400">01100101</div>
                  <div className="animate-pulse delay-600">01101100</div>
                </div>

                {/* Terminal Cursor Animation */}
                <div className="absolute top-4 right-12 flex items-center gap-1">
                  <div className="w-2 h-3 bg-primary-green/40 animate-pulse"></div>
                  <div className="text-xs text-primary-green/30 font-mono animate-fade-in-delayed">_</div>
                </div>

                {/* Network Activity Indicators - moved to avoid overlap */}
                <div className="absolute top-1 left-1/4 flex items-center gap-2">
                  <div className="w-1 h-1 bg-primary-orange/40 rounded-full animate-ping"></div>
                  <div className="w-1 h-1 bg-primary-purple/40 rounded-full animate-ping delay-300"></div>
                  <div className="w-1 h-1 bg-primary-blue/40 rounded-full animate-ping delay-600"></div>
                </div>



                {/* Additional Tech Elements */}
                <div className="absolute top-3 left-1/2 transform -translate-x-1/2 text-xs text-primary-purple/20 font-mono animate-pulse delay-1000">
                  &lt;/&gt;
                </div>

                {/* Scanning Line Effect */}
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-green/30 to-transparent animate-pulse delay-2000"></div>
              </div>
            </div>

            {/* Avatar and Name Section */}
            <div className="flex flex-col md:flex-row items-center gap-8 mb-6">
              {/* Avatar - Replace src with your photo path when ready */}
              <div className="relative">
                <Avatar
                  size="lg"
                  initials="AS"
                  alt="Ansh Sharma"
                  // src="/your-photo.jpg" // Uncomment and add your photo path here
                />
                {/* Enhanced floating elements around avatar */}
                <div className="absolute -top-2 -right-2 w-4 h-4 bg-primary-green/30 rounded-full animate-ping group-hover:bg-primary-green/50"></div>
                <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-primary-blue/40 rounded-full animate-pulse delay-700 group-hover:bg-primary-blue/60"></div>
                <div className="absolute -top-1 -left-2 w-2 h-2 bg-primary-orange/30 rounded-full animate-pulse delay-300 group-hover:animate-ping"></div>
                <div className="absolute -bottom-2 -right-1 w-2 h-2 bg-primary-purple/30 rounded-full animate-pulse delay-1000 group-hover:animate-bounce"></div>
              </div>

              {/* Name and Title with Typewriter Effect */}
              <div className="text-center md:text-left">
                <h1 className="text-4xl md:text-6xl font-bold glow-text bg-gradient-to-r from-primary-blue via-primary-green to-primary-purple bg-clip-text text-transparent animate-gradient min-h-[4rem] md:min-h-[6rem] flex items-center group-hover:scale-110 transition-transform duration-300">
                  <span className="font-mono group-hover:animate-pulse relative">
                    {nameText}
                    {showCursor && nameText.length < fullName.length && (
                      <span className="animate-pulse text-primary-blue group-hover:text-primary-orange">|</span>
                    )}
                    {/* Glitch effect overlay */}
                    <span className="absolute inset-0 opacity-0 group-hover:opacity-20 group-hover:animate-ping text-primary-green">
                      {nameText}
                    </span>
                  </span>
                </h1>
                <div className="text-xl md:text-2xl text-primary-green mt-3 font-medium min-h-[2rem] flex items-center group-hover:text-primary-blue transition-colors duration-300">
                  <span className="font-mono group-hover:animate-pulse relative">
                    {titleText || (isMounted ? fullTitle : '')}
                    {showCursor && titleText.length < fullTitle.length && nameText === fullName && (
                      <span className="animate-pulse text-primary-green group-hover:text-primary-orange">|</span>
                    )}
                    {/* Subtle glow effect */}
                    <span className="absolute inset-0 opacity-0 group-hover:opacity-10 group-hover:animate-pulse text-primary-purple">
                      {titleText || (isMounted ? fullTitle : '')}
                    </span>
                  </span>
                </div>
                <div className="text-sm text-text-secondary mt-4 font-mono opacity-0 animate-fade-in-delayed">
                  Passionate about cloud security and modern web development
                </div>
              </div>
            </div>
          </div>

          {/* Brief Introduction */}
          <div className="max-w-2xl mx-auto text-center">
            <p className="text-text-secondary text-lg leading-relaxed">
              Specializing in cloud infrastructure security and modern web applications.
              I help organizations build secure, scalable solutions that protect their digital assets.
            </p>
          </div>

          {/* Status & Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-background-card/40 border border-border-subtle p-6 rounded-xl hover:border-primary-green/50 transition-all duration-300 hover:bg-background-card/60 hover:scale-105 hover:shadow-lg hover:shadow-primary-green/20 group transform-gpu">
              <div className="text-primary-green font-mono text-sm mb-3 flex items-center gap-2">
                <span className="w-2 h-2 bg-primary-green rounded-full animate-pulse"></span>
                STATUS
              </div>
              <div className="text-text-primary font-semibold group-hover:text-primary-green transition-colors">● AVAILABLE FOR WORK</div>
              <div className="text-xs text-text-secondary mt-1 font-mono">Open to new opportunities</div>
            </div>

            <div className="bg-background-card/40 border border-border-subtle p-6 rounded-xl hover:border-primary-blue/50 transition-all duration-300 hover:bg-background-card/60 hover:scale-105 hover:shadow-lg hover:shadow-primary-blue/20 group transform-gpu">
              <div className="text-primary-blue font-mono text-sm mb-3 flex items-center gap-2">
                <span className="w-2 h-2 bg-primary-blue rounded-full animate-pulse delay-300"></span>
                PROJECTS
              </div>
              <div className="text-text-primary font-semibold group-hover:text-primary-blue transition-colors">6+ PROJECTS</div>
              <div className="text-xs text-text-secondary mt-1 font-mono">Cloud & Web Applications</div>
            </div>

            <div className="bg-background-card/40 border border-border-subtle p-6 rounded-xl hover:border-primary-orange/50 transition-all duration-300 hover:bg-background-card/60 hover:scale-105 hover:shadow-lg hover:shadow-primary-orange/20 group transform-gpu">
              <div className="text-primary-orange font-mono text-sm mb-3 flex items-center gap-2">
                <span className="w-2 h-2 bg-primary-orange rounded-full animate-pulse delay-500"></span>
                TECHNOLOGIES
              </div>
              <div className="text-text-primary font-semibold group-hover:text-primary-orange transition-colors">10+ TECHNOLOGIES</div>
              <div className="text-xs text-text-secondary mt-1 font-mono">Full-stack expertise</div>
            </div>
          </div>

          {/* Tech Stack */}
          <div className="space-y-6">
            <div className="text-text-secondary font-mono text-sm text-center">
              <span className="text-primary-blue">Technologies & Skills</span>
            </div>

            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="group relative">
                <span className="px-4 py-2 bg-primary-blue/20 text-primary-blue rounded-full border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-110 hover:shadow-lg hover:shadow-primary-blue/30 transition-all duration-300 cursor-pointer font-medium flex items-center gap-2">
                  ☁️ AWS
                  <span className="text-xs opacity-70">Advanced</span>
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-primary-green rounded-full animate-pulse opacity-0 group-hover:opacity-100 transition-opacity"></span>
                </span>
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-background-dark border border-primary-blue/30 rounded text-xs text-primary-blue opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  EC2, S3, Lambda, RDS, CloudFormation
                </div>
              </div>

              <div className="group relative">
                <span className="px-4 py-2 bg-primary-blue/20 text-primary-blue rounded-full border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-110 hover:shadow-lg hover:shadow-primary-blue/30 transition-all duration-300 cursor-pointer font-medium flex items-center gap-2">
                  ☁️ Azure
                  <span className="text-xs opacity-70">Basic</span>
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-primary-blue rounded-full animate-pulse opacity-0 group-hover:opacity-100 transition-opacity"></span>
                </span>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-background-dark border border-primary-blue/30 rounded text-xs text-primary-blue opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  VMs, Storage, Functions, DevOps
                </div>
              </div>

              <div className="group relative">
                <span className="px-4 py-2 bg-primary-green/20 text-primary-green rounded-full border border-primary-green/30 hover:bg-primary-green/30 hover:scale-110 hover:shadow-lg hover:shadow-primary-green/30 transition-all duration-300 cursor-pointer font-medium flex items-center gap-2">
                  🛡️ Security
                  <span className="text-xs opacity-70">Advanced</span>
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-primary-orange rounded-full animate-pulse opacity-0 group-hover:opacity-100 transition-opacity"></span>
                </span>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-background-dark border border-primary-green/30 rounded text-xs text-primary-green opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  SIEM, Penetration Testing, SOC
                </div>
              </div>

              <div className="group relative">
                <span className="px-4 py-2 bg-primary-purple/20 text-primary-purple rounded-full border border-primary-purple/30 hover:bg-primary-purple/30 hover:scale-110 hover:shadow-lg hover:shadow-primary-purple/30 transition-all duration-300 cursor-pointer font-medium flex items-center gap-2">
                  ⚛️ React
                  <span className="text-xs opacity-70">Intermediate</span>
                </span>
              </div>

              <div className="group relative">
                <span className="px-4 py-2 bg-primary-orange/20 text-primary-orange rounded-full border border-primary-orange/30 hover:bg-primary-orange/30 hover:scale-110 hover:shadow-lg hover:shadow-primary-orange/30 transition-all duration-300 cursor-pointer font-medium flex items-center gap-2">
                  🚀 Next.js
                  <span className="text-xs opacity-70">Intermediate</span>
                </span>
              </div>

              <div className="group relative">
                <span className="px-4 py-2 bg-primary-purple/20 text-primary-purple rounded-full border border-primary-purple/30 hover:bg-primary-purple/30 hover:scale-110 hover:shadow-lg hover:shadow-primary-purple/30 transition-all duration-300 cursor-pointer font-medium flex items-center gap-2">
                  🔧 DevOps
                  <span className="text-xs opacity-70">Intermediate</span>
                </span>
              </div>
            </div>
          </div>



          {/* Social Links */}
          <div className="flex justify-center space-x-6 mb-8">
            <a
              href="https://github.com/anxious2004"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex items-center justify-center w-12 h-12 bg-background-card/40 border border-border-subtle rounded-full hover:border-primary-blue/50 hover:bg-primary-blue/10 hover:scale-110 transition-all duration-300"
            >
              <svg className="w-5 h-5 text-text-secondary group-hover:text-primary-blue transition-colors" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>

            <a
              href="https://www.instagram.com/ansharmaap2004/"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex items-center justify-center w-12 h-12 bg-background-card/40 border border-border-subtle rounded-full hover:border-primary-orange/50 hover:bg-primary-orange/10 hover:scale-110 transition-all duration-300"
            >
              <svg className="w-5 h-5 text-text-secondary group-hover:text-primary-orange transition-colors" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </a>

            <a
              href="https://x.com/XllAnsh?t=nGdjvxWitsi0BeSp7BbXfg&s=09"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex items-center justify-center w-12 h-12 bg-background-card/40 border border-border-subtle rounded-full hover:border-primary-green/50 hover:bg-primary-green/10 hover:scale-110 transition-all duration-300"
            >
              <svg className="w-5 h-5 text-text-secondary group-hover:text-primary-green transition-colors" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
          </div>

          {/* Call to Action */}
          <div className="space-y-6">
            <div className="text-center">
              <p className="text-text-secondary text-sm max-w-md mx-auto">
                Ready to collaborate on your next project?
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
                className="group px-8 py-3 bg-primary-blue/20 text-primary-blue border border-primary-blue/30 rounded-lg hover:bg-primary-blue/30 hover:scale-105 hover:shadow-lg hover:shadow-primary-blue/30 transition-all duration-300 font-medium flex items-center justify-center gap-2"
              >
                <span>View Projects</span>
                <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              <button
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="group px-8 py-3 bg-primary-green/20 text-primary-green border border-primary-green/30 rounded-lg hover:bg-primary-green/30 hover:scale-105 hover:shadow-lg hover:shadow-primary-green/30 transition-all duration-300 font-medium flex items-center justify-center gap-2"
              >
                <span>Get In Touch</span>
                <svg className="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </button>

              <button
                onClick={downloadResume}
                className="group px-8 py-3 bg-primary-purple/20 text-primary-purple border border-primary-purple/30 rounded-lg hover:bg-primary-purple/30 hover:scale-105 hover:shadow-lg hover:shadow-primary-purple/30 transition-all duration-300 font-medium flex items-center justify-center gap-2"
                title="Download Ansh Sharma's Resume (PDF)"
              >
                <span>Download Resume</span>
                <svg className="w-4 h-4 group-hover:translate-y-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      </section>

      {/* About Section */}
      <section id="about" className={`min-h-screen bg-background-dark relative overflow-hidden transition-all duration-1000 ${isVisible.about ? 'opacity-100 translate-y-0' : 'opacity-70 translate-y-8'}`}>
        {/* Enhanced Background elements */}
        <div className="absolute inset-0 opacity-5">
          {/* Simplified static particles */}
          {[...Array(4)].map((_, i) => (
            <div
              key={`about-particle-${i}`}
              className={`absolute rounded-full animate-pulse ${
                i % 2 === 0 ? 'w-2 h-2 bg-primary-green/20' : 'w-1.5 h-1.5 bg-primary-blue/20'
              }`}
              style={{
                left: `${20 + (i * 20) % 60}%`,
                top: `${20 + (i * 15) % 60}%`,
                animationDelay: `${i * 1}s`
              }}
            />
          ))}

          {/* Animated connecting lines */}
          <div
            className="absolute top-1/4 left-1/4 w-32 h-px bg-gradient-to-r from-transparent via-primary-purple/20 to-transparent animate-pulse"
            style={{
              transform: `translateX(10px)`
            }}
          ></div>
          <div
            className="absolute bottom-1/3 right-1/4 w-24 h-px bg-gradient-to-r from-transparent via-primary-green/20 to-transparent animate-pulse delay-800"
            style={{
              transform: `translateX(-8px)`
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 py-20">
          {/* Enhanced Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-text-primary mb-4 hover:scale-105 transition-transform duration-300">
              About <span className="text-primary-blue">Me</span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary-blue via-primary-green to-primary-purple mx-auto mb-6 animate-pulse"></div>
            <p className="text-text-secondary text-lg max-w-2xl mx-auto leading-relaxed">
              A passionate cybersecurity enthusiast with a strong academic foundation and hands-on experience in building secure systems.
            </p>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">

            {/* Left Column - Stats & Info */}
            <div className="space-y-8">

              {/* Education Card */}
              <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-blue/50 transition-all duration-300">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-primary-blue/20 rounded-lg flex items-center justify-center">
                    <span className="text-primary-blue text-xl">🎓</span>
                  </div>
                  <h3 className="text-xl font-semibold text-text-primary">Education</h3>
                </div>

                <div className="space-y-4">
                  <div className="border-l-2 border-primary-blue/30 pl-4">
                    <h4 className="font-semibold text-text-primary">B.Tech in Computer Science</h4>
                    <p className="text-primary-blue text-sm">Bharati Vidyapeeth's College of Engineering</p>
                    <p className="text-text-secondary text-sm">Currently Pursuing • CGPA: 9.396</p>
                  </div>

                  <div className="border-l-2 border-primary-green/30 pl-4">
                    <h4 className="font-semibold text-text-primary">Class XII (CBSE)</h4>
                    <p className="text-primary-green text-sm">Rajakiya Pratibha Vikas Vidyalaya</p>
                    <p className="text-text-secondary text-sm">2022 • 97.2%</p>
                  </div>

                  <div className="border-l-2 border-primary-purple/30 pl-4">
                    <h4 className="font-semibold text-text-primary">Class X (CBSE)</h4>
                    <p className="text-primary-purple text-sm">Rajakiya Pratibha Vikas Vidyalaya</p>
                    <p className="text-text-secondary text-sm">2020 • 97%</p>
                  </div>
                </div>
              </div>

              {/* Current Role Card */}
              <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-green/50 transition-all duration-300">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-primary-green/20 rounded-lg flex items-center justify-center">
                    <span className="text-primary-green text-xl">💼</span>
                  </div>
                  <h3 className="text-xl font-semibold text-text-primary">Current Role</h3>
                </div>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold text-text-primary">IT Intern</h4>
                    <p className="text-primary-green text-sm">Indian Aviation Academy</p>
                    <p className="text-text-secondary text-sm">Building feedback system for IAA</p>
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <span className="w-2 h-2 bg-primary-green rounded-full animate-pulse"></span>
                    <span className="text-text-secondary">Currently Active</span>
                  </div>
                </div>
              </div>

              {/* Improved Quick Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-background-card/40 border border-border-subtle rounded-xl p-4 text-center hover:border-primary-orange/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-orange/20 hover:scale-110 hover:rotate-1 group cursor-pointer animate-pulse-glow">
                  <div className="text-2xl font-bold text-primary-orange group-hover:scale-125 group-hover:animate-pulse transition-transform duration-300">3+</div>
                  <div className="text-text-secondary text-sm font-medium group-hover:text-primary-orange transition-colors">Years Learning</div>
                </div>
                <div className="bg-background-card/40 border border-border-subtle rounded-xl p-4 text-center hover:border-primary-purple/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-purple/10 hover:scale-105 group cursor-pointer">
                  <div className="text-2xl font-bold text-primary-purple group-hover:scale-110 transition-transform duration-300">9.396</div>
                  <div className="text-text-secondary text-sm font-medium">Current CGPA</div>
                </div>
              </div>
            </div>

            {/* Right Column - Story & Interests */}
            <div className="space-y-8">

              {/* My Story */}
              <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-blue/50 transition-all duration-300">
                <h3 className="text-xl font-semibold text-text-primary mb-4 flex items-center gap-2">
                  <span className="text-primary-blue">📖</span>
                  My Journey
                </h3>

                <div className="space-y-4 text-text-secondary leading-relaxed">
                  <p>
                    My journey into cybersecurity began through an inspiring conversation with a faculty member at my college -
                    an amazing teacher, motivator, and an even better human being who opened my eyes to the fascinating world of digital security.
                  </p>

                  <p>
                    What captivates me most about cybersecurity is <span className="text-primary-green font-medium">how imaginative it is</span>.
                    Every challenge requires creative thinking, whether I'm building port scanners, developing web applications that capture
                    and analyze network packets, or creating systems that detect malicious JavaScript code.
                  </p>

                  <p>
                    Currently, I'm channeling this passion into real-world applications at Indian Aviation Academy, where I'm developing
                    a comprehensive feedback system. This role allows me to combine my technical skills with practical problem-solving.
                  </p>
                </div>
              </div>

              {/* Interests & Approach */}
              <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-green/50 transition-all duration-300">
                <h3 className="text-xl font-semibold text-text-primary mb-4 flex items-center gap-2">
                  <span className="text-primary-green">⚡</span>
                  What Drives Me
                </h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-primary-blue rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-medium text-text-primary">Security-First Mindset</h4>
                      <p className="text-text-secondary text-sm">I focus on projects that enhance network and cybersecurity, from packet analysis to malware detection.</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-medium text-text-primary">Continuous Learning</h4>
                      <p className="text-text-secondary text-sm">Currently preparing for AWS and cybersecurity certifications to strengthen my expertise.</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-primary-purple rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-medium text-text-primary">Creative Problem Solving</h4>
                      <p className="text-text-secondary text-sm">I approach each challenge with imagination and creativity, finding innovative solutions to complex problems.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Personal Interests */}
              <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-orange/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-orange/10 group">
                <h3 className="text-xl font-semibold text-text-primary mb-4 flex items-center gap-2 group-hover:scale-105 transition-transform">
                  <span className="text-primary-orange group-hover:animate-bounce">📚</span>
                  Beyond Code
                </h3>

                <p className="text-text-secondary leading-relaxed group-hover:text-text-primary transition-colors">
                  When I'm not diving deep into cybersecurity challenges, you'll find me immersed in the worlds of
                  <span className="text-primary-orange font-medium hover:bg-primary-orange/20 px-1 rounded transition-all"> fantasy novels, web novels, and light novels</span>.
                  This love for storytelling actually enhances my technical work - it keeps my imagination sharp and helps me
                  think creatively about security scenarios and user experiences.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className={`min-h-screen bg-background-card/20 relative overflow-hidden transition-all duration-1000 ${isVisible.skills ? 'opacity-100 translate-y-0' : 'opacity-70 translate-y-8'}`}>
        {/* Background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-32 left-16 w-2 h-2 bg-primary-green rounded-full animate-pulse"></div>
          <div className="absolute bottom-32 right-16 w-1.5 h-1.5 bg-primary-blue rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 right-20 w-1 h-1 bg-primary-purple rounded-full animate-pulse delay-500"></div>
          <div className="absolute bottom-1/4 left-1/4 w-32 h-px bg-gradient-to-r from-transparent via-primary-orange/20 to-transparent animate-pulse delay-800"></div>
        </div>

        <div className="container mx-auto px-4 py-20">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-text-primary mb-4 hover:scale-105 transition-transform duration-300">
              Technical <span className="text-primary-green">Skills</span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary-green via-primary-blue to-primary-purple mx-auto mb-6 animate-pulse"></div>
            <p className="text-text-secondary text-lg max-w-2xl mx-auto leading-relaxed">
              A comprehensive toolkit spanning full-stack development, cloud infrastructure, and cybersecurity.
            </p>
          </div>

          {/* Skills Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 max-w-7xl mx-auto">

            {/* Programming Languages */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-blue/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-blue/10 hover:scale-105 group ${isVisible.skills ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'}`} style={{transitionDelay: '0.1s'}}>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-primary-blue/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <span className="text-primary-blue text-2xl">💻</span>
                </div>
                <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-blue transition-colors">Programming Languages</h3>
              </div>

              <div className="flex flex-wrap gap-3">
                <span className="px-3 py-2 bg-primary-blue/20 text-primary-blue rounded-lg border border-primary-blue/30 hover:bg-primary-blue/40 hover:scale-110 hover:shadow-lg hover:shadow-primary-blue/30 hover:rotate-1 transition-all duration-300 cursor-pointer font-medium text-sm group-hover:animate-pulse">
                  C++
                </span>
                <span className="px-3 py-2 bg-primary-blue/20 text-primary-blue rounded-lg border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Java
                </span>
                <span className="px-3 py-2 bg-primary-blue/20 text-primary-blue rounded-lg border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Python
                </span>
                <span className="px-3 py-2 bg-primary-blue/20 text-primary-blue rounded-lg border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  JavaScript
                </span>
                <span className="px-3 py-2 bg-primary-blue/20 text-primary-blue rounded-lg border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Rust
                </span>
              </div>
            </div>

            {/* Web Development */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-green/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-green/10 hover:scale-105 group ${isVisible.skills ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'}`} style={{transitionDelay: '0.2s'}}>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-primary-green/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <span className="text-primary-green text-2xl">🌐</span>
                </div>
                <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-green transition-colors">Web Development</h3>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-text-primary mb-2">Frontend</h4>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                      HTML
                    </span>
                    <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                      CSS
                    </span>
                    <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                      React
                    </span>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-text-primary mb-2">Backend</h4>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                      Node.js
                    </span>
                    <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                      Express
                    </span>
                    <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                      Django
                    </span>
                    <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                      Flask
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Cloud & DevOps */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-orange/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-orange/10 hover:scale-105 group ${isVisible.skills ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'}`} style={{transitionDelay: '0.3s'}}>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-primary-orange/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <span className="text-primary-orange text-2xl">☁️</span>
                </div>
                <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-orange transition-colors">Cloud & DevOps</h3>
              </div>

              <div className="flex flex-wrap gap-3">
                <span className="px-3 py-2 bg-primary-orange/20 text-primary-orange rounded-lg border border-primary-orange/30 hover:bg-primary-orange/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  AWS EC2
                </span>
                <span className="px-3 py-2 bg-primary-orange/20 text-primary-orange rounded-lg border border-primary-orange/30 hover:bg-primary-orange/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  AWS S3
                </span>
                <span className="px-3 py-2 bg-primary-orange/20 text-primary-orange rounded-lg border border-primary-orange/30 hover:bg-primary-orange/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  AWS Lambda
                </span>
                <span className="px-3 py-2 bg-primary-orange/20 text-primary-orange rounded-lg border border-primary-orange/30 hover:bg-primary-orange/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Azure
                </span>
                <span className="px-3 py-2 bg-primary-orange/20 text-primary-orange rounded-lg border border-primary-orange/30 hover:bg-primary-orange/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Docker
                </span>
                <span className="px-3 py-2 bg-primary-orange/20 text-primary-orange rounded-lg border border-primary-orange/30 hover:bg-primary-orange/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  CI/CD
                </span>
              </div>
            </div>

            {/* Databases */}
            <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-purple/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-purple/10 group">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-primary-purple/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <span className="text-primary-purple text-2xl">🗄️</span>
                </div>
                <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-purple transition-colors">Databases</h3>
              </div>

              <div className="flex flex-wrap gap-3">
                <span className="px-3 py-2 bg-primary-purple/20 text-primary-purple rounded-lg border border-primary-purple/30 hover:bg-primary-purple/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  MySQL
                </span>
                <span className="px-3 py-2 bg-primary-purple/20 text-primary-purple rounded-lg border border-primary-purple/30 hover:bg-primary-purple/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  MongoDB
                </span>
              </div>
            </div>

            {/* Cybersecurity & Tools */}
            <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-green/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-green/10 group">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-primary-green/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <span className="text-primary-green text-2xl">🛡️</span>
                </div>
                <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-green transition-colors">Cybersecurity & Tools</h3>
              </div>

              <div className="flex flex-wrap gap-3">
                <span className="px-3 py-2 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Linux
                </span>
                <span className="px-3 py-2 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Wireshark
                </span>
                <span className="px-3 py-2 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Bash Scripting
                </span>
                <span className="px-3 py-2 bg-primary-green/20 text-primary-green rounded-lg border border-primary-green/30 hover:bg-primary-green/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  NPM
                </span>
              </div>
            </div>

            {/* Version Control & Development */}
            <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-blue/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-blue/10 group">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-primary-blue/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <span className="text-primary-blue text-2xl">🔧</span>
                </div>
                <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-blue transition-colors">Development Tools</h3>
              </div>

              <div className="flex flex-wrap gap-3">
                <span className="px-3 py-2 bg-primary-blue/20 text-primary-blue rounded-lg border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Git
                </span>
                <span className="px-3 py-2 bg-primary-blue/20 text-primary-blue rounded-lg border border-primary-blue/30 hover:bg-primary-blue/30 hover:scale-105 transition-all duration-300 cursor-pointer font-medium text-sm">
                  Scripting
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className={`min-h-screen bg-background-navy/20 relative overflow-hidden transition-all duration-1000 ${isVisible.projects ? 'opacity-100 translate-y-0' : 'opacity-70 translate-y-8'}`}>
        {/* Background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-24 right-24 w-2 h-2 bg-primary-orange rounded-full animate-pulse"></div>
          <div className="absolute bottom-24 left-24 w-1.5 h-1.5 bg-primary-green rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-16 w-1 h-1 bg-primary-blue rounded-full animate-pulse delay-500"></div>
          <div className="absolute top-1/3 right-1/4 w-24 h-px bg-gradient-to-r from-transparent via-primary-purple/20 to-transparent animate-pulse delay-700"></div>
        </div>

        <div className="container mx-auto px-4 py-20">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-text-primary mb-4 hover:scale-105 transition-transform duration-300">
              Featured <span className="text-primary-orange">Projects</span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary-orange via-primary-purple to-primary-green mx-auto mb-6 animate-pulse"></div>
            <p className="text-text-secondary text-lg max-w-2xl mx-auto leading-relaxed">
              A showcase of practical applications spanning cybersecurity, web development, and social platforms.
            </p>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">

            {/* SafeSearch Project */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl overflow-hidden hover:border-primary-green/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-green/20 hover:scale-105 hover:rotate-1 group ${isVisible.projects ? 'opacity-100 translate-y-0 rotate-0' : 'opacity-0 translate-y-12 rotate-3'}`} style={{transitionDelay: '0.1s'}}>
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary-green/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                    <span className="text-primary-green text-2xl">🛡️</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-green transition-colors">SafeSearch</h3>
                    <p className="text-primary-green text-sm font-medium">Web Browsing Security Framework</p>
                  </div>
                </div>

                <p className="text-text-secondary text-sm leading-relaxed mb-4">
                  A comprehensive security framework designed to protect users during web browsing by detecting and preventing malicious activities in real-time.
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs font-medium">Security</span>
                  <span className="px-2 py-1 bg-primary-blue/20 text-primary-blue rounded text-xs font-medium">Web Framework</span>
                  <span className="px-2 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs font-medium">Real-time Protection</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-text-secondary">Status: In Development</span>
                  <div className="flex gap-2">
                    <button className="px-3 py-1 bg-primary-green/20 text-primary-green rounded text-xs hover:bg-primary-green/30 transition-colors">
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Snacx Project */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl overflow-hidden hover:border-primary-blue/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-blue/20 hover:scale-105 hover:-rotate-1 group ${isVisible.projects ? 'opacity-100 translate-y-0 rotate-0' : 'opacity-0 translate-y-12 -rotate-3'}`} style={{transitionDelay: '0.2s'}}>
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary-blue/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                    <span className="text-primary-blue text-2xl">😄</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-blue transition-colors">Snacx</h3>
                    <p className="text-primary-blue text-sm font-medium">Social Media Platform</p>
                  </div>
                </div>

                <p className="text-text-secondary text-sm leading-relaxed mb-4">
                  A modern social media platform specifically designed for sharing and discovering memes, featuring user interactions, content curation, and community engagement.
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-2 py-1 bg-primary-blue/20 text-primary-blue rounded text-xs font-medium">Social Media</span>
                  <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs font-medium">Full-Stack</span>
                  <span className="px-2 py-1 bg-primary-orange/20 text-primary-orange rounded text-xs font-medium">Community</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-text-secondary">Status: Active Development</span>
                  <div className="flex gap-2">
                    <button className="px-3 py-1 bg-primary-blue/20 text-primary-blue rounded text-xs hover:bg-primary-blue/30 transition-colors">
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Trainee Feedback System */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl overflow-hidden hover:border-primary-orange/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-orange/20 hover:scale-105 hover:rotate-1 group ${isVisible.projects ? 'opacity-100 translate-y-0 rotate-0' : 'opacity-0 translate-y-12 rotate-2'}`} style={{transitionDelay: '0.3s'}}>
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary-orange/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                    <span className="text-primary-orange text-2xl">📋</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-orange transition-colors">Trainee Feedback System</h3>
                    <p className="text-primary-orange text-sm font-medium">Enterprise Management System</p>
                  </div>
                </div>

                <p className="text-text-secondary text-sm leading-relaxed mb-4">
                  A comprehensive feedback management system for Indian Aviation Academy, streamlining trainee evaluations, progress tracking, and institutional reporting.
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-2 py-1 bg-primary-orange/20 text-primary-orange rounded text-xs font-medium">Enterprise</span>
                  <span className="px-2 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs font-medium">Management</span>
                  <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs font-medium">Database</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-text-secondary">Status: In Production</span>
                  <div className="flex gap-2">
                    <button className="px-3 py-1 bg-primary-orange/20 text-primary-orange rounded text-xs hover:bg-primary-orange/30 transition-colors">
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Hostile JS Playground */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl overflow-hidden hover:border-primary-purple/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-purple/20 hover:scale-105 hover:-rotate-1 group ${isVisible.projects ? 'opacity-100 translate-y-0 rotate-0' : 'opacity-0 translate-y-12 -rotate-2'}`} style={{transitionDelay: '0.4s'}}>
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary-purple/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                    <span className="text-primary-purple text-2xl">⚠️</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-purple transition-colors">Hostile JS Playground</h3>
                    <p className="text-primary-purple text-sm font-medium">Malware Analysis Tool</p>
                  </div>
                </div>

                <p className="text-text-secondary text-sm leading-relaxed mb-4">
                  A secure sandbox environment for analyzing and testing malicious JavaScript code, providing insights into potential threats and attack vectors.
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-2 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs font-medium">Malware Analysis</span>
                  <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs font-medium">Security Research</span>
                  <span className="px-2 py-1 bg-primary-orange/20 text-primary-orange rounded text-xs font-medium">Sandbox</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-text-secondary">Status: Research Phase</span>
                  <div className="flex gap-2">
                    <button className="px-3 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs hover:bg-primary-purple/30 transition-colors">
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Multiport Scanning System */}
            <div className={`bg-background-card/40 border border-border-subtle rounded-xl overflow-hidden hover:border-primary-green/50 transition-all duration-500 hover:shadow-lg hover:shadow-primary-green/20 hover:scale-105 hover:rotate-1 group md:col-span-2 lg:col-span-1 ${isVisible.projects ? 'opacity-100 translate-y-0 rotate-0' : 'opacity-0 translate-y-12 rotate-3'}`} style={{transitionDelay: '0.5s'}}>
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-primary-green/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                    <span className="text-primary-green text-2xl">🔍</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-text-primary group-hover:text-primary-green transition-colors">Multiport Security Scanner</h3>
                    <p className="text-primary-green text-sm font-medium">Network Security Tool</p>
                  </div>
                </div>

                <p className="text-text-secondary text-sm leading-relaxed mb-4">
                  An advanced network scanning and security assessment tool capable of multi-port analysis, vulnerability detection, and comprehensive network mapping.
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs font-medium">Network Security</span>
                  <span className="px-2 py-1 bg-primary-blue/20 text-primary-blue rounded text-xs font-medium">Port Scanning</span>
                  <span className="px-2 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs font-medium">Vulnerability Assessment</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-text-secondary">Status: Testing Phase</span>
                  <div className="flex gap-2">
                    <button className="px-3 py-1 bg-primary-green/20 text-primary-green rounded text-xs hover:bg-primary-green/30 transition-colors">
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <p className="text-text-secondary mb-6">
              Interested in learning more about these projects or discussing potential collaborations?
            </p>
            <button
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="px-8 py-3 bg-primary-orange/20 text-primary-orange border border-primary-orange/30 rounded-lg hover:bg-primary-orange/30 hover:scale-105 hover:shadow-lg hover:shadow-primary-orange/30 transition-all duration-300 font-medium"
            >
              Get In Touch
            </button>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section id="experience" className={`min-h-screen bg-background-card/20 relative overflow-hidden transition-all duration-1000 ${isVisible.experience ? 'opacity-100 translate-y-0' : 'opacity-70 translate-y-8'}`}>
        {/* Background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-2 h-2 bg-primary-purple rounded-full animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-1.5 h-1.5 bg-primary-orange rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 right-16 w-1 h-1 bg-primary-green rounded-full animate-pulse delay-500"></div>
          <div className="absolute bottom-1/3 left-1/4 w-32 h-px bg-gradient-to-r from-transparent via-primary-blue/20 to-transparent animate-pulse delay-800"></div>
        </div>

        <div className="container mx-auto px-4 py-20">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-text-primary mb-4 hover:scale-105 transition-transform duration-300">
              Experience <span className="text-primary-purple">Timeline</span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary-purple via-primary-orange to-primary-blue mx-auto mb-6 animate-pulse"></div>
            <p className="text-text-secondary text-lg max-w-2xl mx-auto leading-relaxed">
              A timeline of educational milestones and experience in technology and cybersecurity.
            </p>
          </div>

          {/* Experience Timeline */}
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-purple via-primary-orange to-primary-green opacity-30"></div>

              {/* Timeline Items */}
              <div className="space-y-12">

                {/* Current - IAA Internship */}
                <div className="relative flex items-start gap-8 group">
                  <div className="w-16 h-16 bg-primary-green/20 rounded-full border-4 border-background-dark flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform">
                    <span className="text-primary-green text-2xl">💼</span>
                  </div>
                  <div className="flex-1 bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-green/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-green/10">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="px-3 py-1 bg-primary-green/20 text-primary-green rounded-full text-sm font-medium">Current</span>
                      <span className="text-text-secondary text-sm">2024 - Present</span>
                    </div>
                    <h3 className="text-xl font-semibold text-text-primary mb-2 group-hover:text-primary-green transition-colors">IT Intern</h3>
                    <p className="text-primary-green font-medium mb-3">Indian Aviation Academy</p>
                    <p className="text-text-secondary leading-relaxed mb-4">
                      Developing a comprehensive feedback management system for trainee evaluations and institutional reporting.
                      Working with modern web technologies to create scalable solutions for educational administration.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs">System Development</span>
                      <span className="px-2 py-1 bg-primary-blue/20 text-primary-blue rounded text-xs">Database Management</span>
                      <span className="px-2 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs">Web Development</span>
                    </div>
                  </div>
                </div>

                {/* B.Tech Start */}
                <div className="relative flex items-start gap-8 group">
                  <div className="w-16 h-16 bg-primary-blue/20 rounded-full border-4 border-background-dark flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform">
                    <span className="text-primary-blue text-2xl">🎓</span>
                  </div>
                  <div className="flex-1 bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-blue/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-blue/10">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="px-3 py-1 bg-primary-blue/20 text-primary-blue rounded-full text-sm font-medium">In Progress</span>
                      <span className="text-text-secondary text-sm">2022 - 2026</span>
                    </div>
                    <h3 className="text-xl font-semibold text-text-primary mb-2 group-hover:text-primary-blue transition-colors">B.Tech in Computer Science</h3>
                    <p className="text-primary-blue font-medium mb-3">Bharati Vidyapeeth's College of Engineering</p>
                    <p className="text-text-secondary leading-relaxed mb-4">
                      Pursuing comprehensive education in computer science with focus on cybersecurity, web development, and cloud technologies.
                      Maintaining excellent academic performance with CGPA of 9.396.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-primary-blue/20 text-primary-blue rounded text-xs">Computer Science</span>
                      <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs">Cybersecurity</span>
                      <span className="px-2 py-1 bg-primary-orange/20 text-primary-orange rounded text-xs">CGPA: 9.396</span>
                    </div>
                  </div>
                </div>

                {/* Academic Foundation */}
                <div className="relative flex items-start gap-8 group">
                  <div className="w-16 h-16 bg-primary-orange/20 rounded-full border-4 border-background-dark flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform">
                    <span className="text-primary-orange text-2xl">📚</span>
                  </div>
                  <div className="flex-1 bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-orange/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-orange/10">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="px-3 py-1 bg-primary-orange/20 text-primary-orange rounded-full text-sm font-medium">Completed</span>
                      <span className="text-text-secondary text-sm">2020 - 2022</span>
                    </div>
                    <h3 className="text-xl font-semibold text-text-primary mb-2 group-hover:text-primary-orange transition-colors">Academic Foundation</h3>
                    <p className="text-primary-orange font-medium mb-3">Rajakiya Pratibha Vikas Vidyalaya</p>
                    <p className="text-text-secondary leading-relaxed mb-4">
                      Built strong academic foundation with exceptional performance in Class X (97%) and Class XII (97.2%).
                      Developed early interest in technology and problem-solving that led to pursuing computer science.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-primary-orange/20 text-primary-orange rounded text-xs">Class XII: 97.2%</span>
                      <span className="px-2 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs">Class X: 97%</span>
                      <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs">CBSE Board</span>
                    </div>
                  </div>
                </div>

                {/* Future Goals */}
                <div className="relative flex items-start gap-8 group">
                  <div className="w-16 h-16 bg-primary-purple/20 rounded-full border-4 border-background-dark flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform">
                    <span className="text-primary-purple text-2xl">🚀</span>
                  </div>
                  <div className="flex-1 bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-purple/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-purple/10">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="px-3 py-1 bg-primary-purple/20 text-primary-purple rounded-full text-sm font-medium">Upcoming</span>
                      <span className="text-text-secondary text-sm">2024 - 2026</span>
                    </div>
                    <h3 className="text-xl font-semibold text-text-primary mb-2 group-hover:text-primary-purple transition-colors">Professional Development</h3>
                    <p className="text-primary-purple font-medium mb-3">Continuous Learning & Growth</p>
                    <p className="text-text-secondary leading-relaxed mb-4">
                      Actively preparing for AWS and cybersecurity certifications while seeking opportunities in cloud security,
                      web development, and cybersecurity roles. Open to internships, full-time positions, and collaborative projects.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-primary-purple/20 text-primary-purple rounded text-xs">AWS Certification</span>
                      <span className="px-2 py-1 bg-primary-green/20 text-primary-green rounded text-xs">Cybersecurity</span>
                      <span className="px-2 py-1 bg-primary-blue/20 text-primary-blue rounded text-xs">Career Ready</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className={`min-h-screen bg-background-navy/20 relative overflow-hidden transition-all duration-1000 ${isVisible.contact ? 'opacity-100 translate-y-0' : 'opacity-70 translate-y-8'}`}>
        {/* Background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-16 right-16 w-2 h-2 bg-primary-blue rounded-full animate-pulse"></div>
          <div className="absolute bottom-16 left-16 w-1.5 h-1.5 bg-primary-green rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/3 left-20 w-1 h-1 bg-primary-orange rounded-full animate-pulse delay-500"></div>
          <div className="absolute bottom-1/4 right-1/3 w-28 h-px bg-gradient-to-r from-transparent via-primary-purple/20 to-transparent animate-pulse delay-700"></div>
        </div>

        <div className="container mx-auto px-4 py-20">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-text-primary mb-4 hover:scale-105 transition-transform duration-300">
              Get In <span className="text-primary-blue">Touch</span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary-blue via-primary-green to-primary-purple mx-auto mb-6 animate-pulse"></div>
            <p className="text-text-secondary text-lg max-w-2xl mx-auto leading-relaxed">
              Ready to collaborate on exciting projects or discuss opportunities in cybersecurity and web development.
            </p>
          </div>

          {/* Contact Content */}
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">

              {/* Contact Information */}
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-semibold text-text-primary mb-6 flex items-center gap-3">
                    <span className="text-primary-blue">📧</span>
                    Contact Information
                  </h3>

                  {/* Email */}
                  <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-blue/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-blue/10 group mb-6">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-primary-blue/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span className="text-primary-blue text-xl">✉️</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-text-primary group-hover:text-primary-blue transition-colors">Email</h4>
                        <span className="text-primary-blue font-medium">
                          <EMAIL>
                        </span>
                        <p className="text-text-secondary text-sm mt-1">Preferred contact method • Quick response</p>
                      </div>
                    </div>
                  </div>

                  {/* Social Links - Coming Soon */}
                  <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-green/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-green/10 group mb-6">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-primary-green/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span className="text-primary-green text-xl">🔗</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-text-primary group-hover:text-primary-green transition-colors">Social & Professional</h4>
                        <div className="space-y-1">
                          <p className="text-text-secondary text-sm">GitHub: <a href="https://github.com/anxious2004" target="_blank" rel="noopener noreferrer" className="text-primary-green hover:underline">anxious2004</a></p>
                          <p className="text-text-secondary text-sm">Twitter: <a href="https://x.com/XllAnsh?t=nGdjvxWitsi0BeSp7BbXfg&s=09" target="_blank" rel="noopener noreferrer" className="text-primary-green hover:underline">@XllAnsh</a></p>
                          <p className="text-text-secondary text-sm">Instagram: <a href="https://www.instagram.com/ansharmaap2004/" target="_blank" rel="noopener noreferrer" className="text-primary-green hover:underline">@ansharmaap2004</a></p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Availability */}
                  <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-orange/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-orange/10 group">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-primary-orange/20 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span className="text-primary-orange text-xl">🚀</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-text-primary group-hover:text-primary-orange transition-colors">Availability</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="w-2 h-2 bg-primary-green rounded-full animate-pulse"></span>
                          <span className="text-primary-green font-medium text-sm">Open to Opportunities</span>
                        </div>
                        <p className="text-text-secondary text-sm mt-1">Internships • Full-time • Freelance • Remote/On-site</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Call to Action */}
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-semibold text-text-primary mb-6 flex items-center gap-3">
                    <span className="text-primary-purple">🤝</span>
                    Let's Collaborate
                  </h3>

                  <div className="bg-background-card/40 border border-border-subtle rounded-xl p-8 hover:border-primary-purple/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary-purple/10">
                    <div className="text-center space-y-6">
                      <div className="w-20 h-20 bg-primary-purple/20 rounded-full flex items-center justify-center mx-auto">
                        <span className="text-primary-purple text-3xl">💡</span>
                      </div>

                      <div>
                        <h4 className="text-xl font-semibold text-text-primary mb-3">Ready to Work Together?</h4>
                        <p className="text-text-secondary leading-relaxed mb-6">
                          Whether you're looking for a cybersecurity specialist, a full-stack developer, or someone passionate about
                          building secure and scalable solutions, I'd love to hear from you.
                        </p>
                      </div>

                      <div className="space-y-6">
                        <div className="text-center p-6 bg-background-card/20 rounded-lg border border-border-subtle/30">
                          <div className="text-primary-blue font-semibold text-lg mb-2">Ready to Connect?</div>
                          <div className="text-text-secondary text-sm mb-4">Feel free to reach out via email for any opportunities or collaborations</div>
                          <div className="text-primary-green font-medium"><EMAIL></div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-background-card/20 rounded-lg border border-border-subtle/30">
                            <div className="text-primary-green font-semibold">Quick Response</div>
                            <div className="text-text-secondary text-sm">Usually within 24h</div>
                          </div>
                          <div className="text-center p-4 bg-background-card/20 rounded-lg border border-border-subtle/30">
                            <div className="text-primary-orange font-semibold">Flexible</div>
                            <div className="text-text-secondary text-sm">Any work arrangement</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Current Focus */}
                <div className="bg-background-card/40 border border-border-subtle rounded-xl p-6 hover:border-primary-green/50 transition-all duration-300">
                  <h4 className="font-semibold text-text-primary mb-4 flex items-center gap-2">
                    <span className="text-primary-green">🎯</span>
                    Currently Interested In
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <span className="w-2 h-2 bg-primary-blue rounded-full"></span>
                      <span className="text-text-secondary text-sm">Cybersecurity roles and projects</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="w-2 h-2 bg-primary-green rounded-full"></span>
                      <span className="text-text-secondary text-sm">Full-stack web development opportunities</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="w-2 h-2 bg-primary-orange rounded-full"></span>
                      <span className="text-text-secondary text-sm">Cloud infrastructure and DevOps projects</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="w-2 h-2 bg-primary-purple rounded-full"></span>
                      <span className="text-text-secondary text-sm">Open source contributions and collaborations</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
