@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

@import "tailwindcss";

:root {
  --background: #0a0a0f;
  --foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: "Inter", system-ui, sans-serif;
  --font-mono: "JetBrains Mono", "Consolas", "Monaco", "Courier New", monospace;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Performance optimizations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* GPU acceleration for animations */
.will-change-transform {
  will-change: transform;
}

.will-change-auto {
  will-change: auto;
}

/* Optimize repaints and compositing */
.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1b2e;
}

::-webkit-scrollbar-thumb {
  background: #00d4ff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00ff88;
}

/* Grid pattern background */
.grid-pattern {
  background-image: linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Enhanced Glowing text effect */
.glow-text {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor,
    0 0 30px currentColor;
  transition: text-shadow 0.3s ease, transform 0.3s ease;
}

.glow-text:hover {
  text-shadow: 0 0 15px currentColor, 0 0 25px currentColor,
    0 0 35px currentColor, 0 0 45px currentColor;
  transform: scale(1.02);
}

/* Terminal cursor animation */
.terminal-cursor::after {
  content: "|";
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Holographic border effect */
.holographic-border {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(45deg, #00d4ff, #00ff88, #8b5cf6) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: exclude;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Floating animation for background elements */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
  }
  66% {
    transform: translateY(5px) rotate(240deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 8s ease-in-out infinite;
  animation-delay: 2s;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Subtle glow effect */
.subtle-glow {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);
}

.subtle-glow:hover {
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.2);
}

/* Mobile menu animations */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navigation hover effects */
@keyframes slideIn {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Header backdrop blur enhancement */
.header-backdrop {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
}

/* Phase 3 Hero Section Animations */
@keyframes fadeInDelayed {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-delayed {
  animation: fadeInDelayed 1s ease-out 3s forwards;
}

/* Enhanced 3D card effects */
.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Data stream animations */
@keyframes dataStream {
  0% {
    opacity: 0.3;
    transform: translateY(-10px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(0px);
  }
  100% {
    opacity: 0.3;
    transform: translateY(10px);
  }
}

.animate-data-stream {
  animation: dataStream 4s ease-in-out infinite;
}

/* Typewriter cursor */
@keyframes typewriterCursor {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.typewriter-cursor {
  animation: typewriterCursor 1s infinite;
}

/* Enhanced glow effects for interactive elements */
.glow-on-hover:hover {
  box-shadow: 0 0 20px currentColor;
  filter: brightness(1.2);
}

/* Grid pattern background */
.bg-grid-pattern {
  background-image: linear-gradient(
      rgba(0, 212, 255, 0.03) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Progress bar animations */
@keyframes progressFill {
  0% {
    width: 0%;
  }
  100% {
    width: var(--progress-width);
  }
}

.progress-bar {
  animation: progressFill 2s ease-out 1s forwards;
}

/* Tooltip animations */
@keyframes tooltipFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.tooltip-enter {
  animation: tooltipFadeIn 0.2s ease-out forwards;
}

/* Activity feed scroll animation */
@keyframes activitySlide {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.activity-item {
  animation: activitySlide 0.5s ease-out forwards;
}

.activity-item:nth-child(2) {
  animation-delay: 0.2s;
}

.activity-item:nth-child(3) {
  animation-delay: 0.4s;
}

/* Enhanced particle interactions */
.particle-interactive {
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.particle-interactive:hover {
  filter: brightness(1.5) blur(0.5px);
  transform: scale(1.2);
}

/* Advanced keyframe animations */
@keyframes float-enhanced {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-10px) rotate(2deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(0deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-10px) rotate(-2deg);
    opacity: 1;
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 5px currentColor;
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 15px currentColor, 0 0 25px currentColor;
    transform: scale(1.05);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Utility classes for enhanced animations */
.animate-float-enhanced {
  animation: float-enhanced 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Hover effects for enhanced interactivity */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hover-glow {
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px currentColor;
  transform: scale(1.05);
}
