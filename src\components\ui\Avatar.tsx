import Image from 'next/image';

interface AvatarProps {
  src?: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showScanLine?: boolean;
  initials?: string;
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
  xl: 'w-40 h-40',
};

export default function Avatar({
  src,
  alt = "Avatar",
  size = 'md',
  className = '',
  showScanLine = true,
  initials = "AS"
}: AvatarProps) {
  const sizeClass = sizeClasses[size];

  return (
    <div className={`relative ${className}`}>
      {/* Glowing border */}
      <div className={`${sizeClass} rounded-full bg-gradient-to-br from-primary-blue via-primary-green to-primary-purple p-1 animate-pulse-glow`}>
        {/* Avatar content */}
        <div className="w-full h-full rounded-full bg-background-dark overflow-hidden flex items-center justify-center">
          {src ? (
            <Image
              src={src}
              alt={alt}
              fill
              className="object-cover rounded-full"
              loading="lazy"
              quality={85}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="text-2xl md:text-4xl font-bold text-primary-blue font-mono">
              {initials}
            </div>
          )}
        </div>
      </div>

      {/* Scanning line effect */}
      {showScanLine && (
        <div className="absolute inset-0 rounded-full overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-0.5 bg-primary-green animate-scan-line opacity-75"></div>
        </div>
      )}
    </div>
  );
}
