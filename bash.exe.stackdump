Stack trace:
Frame         Function      Args
0007FFFF9960  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8860) msys-2.0.dll+0x2118E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9960  00021006A545 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCF44E0000 ntdll.dll
7FFCF2CA0000 KERNEL32.DLL
7FFCF1A70000 KERNELBASE.dll
7FFCF2280000 USER32.dll
7FFCF1630000 win32u.dll
7FFCF3AC0000 GDI32.dll
000210040000 msys-2.0.dll
7FFCF1660000 gdi32full.dll
7FFCF2050000 msvcp_win.dll
7FFCF1920000 ucrtbase.dll
7FFCF30A0000 advapi32.dll
7FFCF3AF0000 msvcrt.dll
7FFCF2D90000 sechost.dll
7FFCF39A0000 RPCRT4.dll
7FFCF0C30000 CRYPTBASE.DLL
7FFCF1FB0000 bcryptPrimitives.dll
7FFCF3170000 IMM32.DLL
