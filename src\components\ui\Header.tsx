'use client';

import { useState, useEffect } from 'react';

const Header = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Skills', href: '#skills' },
    { name: 'Projects', href: '#projects' },
    { name: 'Experience', href: '#experience' },
    { name: 'Contact', href: '#contact' },
  ];

  useEffect(() => {
    const controlNavbar = () => {
      if (typeof window !== 'undefined') {
        if (window.scrollY > lastScrollY && window.scrollY > 100) {
          // Scrolling down & past 100px
          setIsVisible(false);
        } else {
          // Scrolling up
          setIsVisible(true);
        }
        setLastScrollY(window.scrollY);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', controlNavbar);
      return () => {
        window.removeEventListener('scroll', controlNavbar);
      };
    }
  }, [lastScrollY]);

  // ESC key to close mobile menu
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('keydown', handleEscKey);
      return () => {
        document.removeEventListener('keydown', handleEscKey);
      };
    }
  }, [isMobileMenuOpen]);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      {/* Main Header */}
      <header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-out backdrop-blur-md ${
          isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
        }`}
      >
        <div className="bg-background-dark/80 header-backdrop border-b border-border-subtle shadow-lg hover:shadow-xl hover:shadow-primary-blue/10 transition-all duration-300">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              
              {/* Logo */}
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-blue via-primary-green to-primary-purple p-0.5 animate-pulse-glow">
                    <div className="w-full h-full rounded-lg bg-background-dark flex items-center justify-center">
                      <span className="text-primary-blue font-mono font-bold text-lg">AS</span>
                    </div>
                  </div>
                  {/* Scanning line */}
                  <div className="absolute inset-0 rounded-lg overflow-hidden">
                    <div className="absolute top-0 left-0 w-full h-0.5 bg-primary-green animate-scan-line opacity-75"></div>
                  </div>
                </div>
                <div className="hidden sm:block">
                  <span className="text-text-primary font-mono text-sm hover:text-primary-blue transition-colors duration-200">Ansh Sharma</span>
                  <div className="text-primary-green text-xs flex items-center">
                    <span>$ ready --for-work</span>
                    <span className="ml-1 animate-pulse">_</span>
                  </div>
                </div>
              </div>

              {/* Desktop Navigation */}
              <nav className="hidden md:flex items-center space-x-1">
                {navItems.map((item, index) => (
                  <button
                    key={item.name}
                    onClick={() => scrollToSection(item.href)}
                    className="relative px-4 py-2 text-text-secondary hover:text-primary-blue font-mono text-sm transition-all duration-300 hover:bg-background-card/30 rounded-lg border border-transparent hover:border-border-subtle group overflow-hidden"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {/* Scanning line effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-blue/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>

                    <span className="relative z-10">
                      <span className="group-hover:text-primary-green transition-colors duration-200">$</span>
                      <span className="ml-1 group-hover:tracking-wider transition-all duration-200">{item.name.toLowerCase()}</span>
                    </span>

                    {/* Active indicator dot */}
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-green rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                  </button>
                ))}
              </nav>

              {/* Status Indicator & Quick Actions */}
              <div className="hidden lg:flex items-center space-x-4">
                {/* Status */}
                <div className="flex items-center space-x-2 text-xs bg-background-card/30 px-3 py-1.5 rounded-full border border-border-subtle">
                  <div className="w-2 h-2 bg-primary-green rounded-full animate-pulse"></div>
                  <span className="text-primary-green font-mono">AVAILABLE</span>
                </div>

                {/* Quick Contact */}
                <button
                  onClick={() => scrollToSection('#contact')}
                  className="relative text-xs font-mono text-text-secondary hover:text-primary-orange transition-all duration-200 px-3 py-1.5 border border-border-subtle hover:border-primary-orange/50 rounded-full hover:bg-primary-orange/10 hover:scale-105 group"
                >
                  HIRE ME
                  {/* Notification dot */}
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary-green rounded-full animate-pulse group-hover:bg-primary-orange transition-colors duration-200"></div>
                </button>
              </div>

              {/* Mobile Menu Button */}
              <div className="md:hidden flex items-center space-x-3">
                {/* Mobile status indicator */}
                <div className="flex items-center space-x-1">
                  <div className="w-1.5 h-1.5 bg-primary-green rounded-full animate-pulse"></div>
                </div>

                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="p-2 text-text-secondary hover:text-primary-blue transition-colors duration-200 border border-border-subtle hover:border-primary-blue/50 rounded-lg hover:bg-background-card/30"
                >
                  <div className="w-5 h-5 flex flex-col justify-center items-center space-y-1">
                    <div className={`w-4 h-0.5 bg-current transition-all duration-300 ${isMobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></div>
                    <div className={`w-4 h-0.5 bg-current transition-all duration-300 ${isMobileMenuOpen ? 'opacity-0' : ''}`}></div>
                    <div className={`w-4 h-0.5 bg-current transition-all duration-300 ${isMobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Terminal Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-background-dark/95 backdrop-blur-md"
            onClick={() => setIsMobileMenuOpen(false)}
          ></div>

          {/* Menu Content */}
          <div className="relative h-full flex flex-col justify-center items-center space-y-8 font-mono">
            {/* Header */}
            <div className="text-center mb-4">
              <div className="text-primary-blue text-sm mb-2">
                <span className="text-primary-green">$</span> navigation --mobile
              </div>
              <div className="text-xs text-text-secondary">
                <span className="text-primary-orange">●</span> SECURE CONNECTION ESTABLISHED
              </div>
            </div>

            {/* Navigation Items */}
            <div className="space-y-6">
              {navItems.map((item, index) => (
                <button
                  key={item.name}
                  onClick={() => scrollToSection(item.href)}
                  className="block text-2xl text-text-secondary hover:text-primary-blue transition-all duration-300 hover:scale-110 hover:tracking-wider group"
                  style={{
                    animationDelay: `${index * 100}ms`,
                    opacity: 0,
                    animation: `fadeInUp 0.5s ease-out ${index * 100}ms forwards`
                  }}
                >
                  <span className="text-primary-green group-hover:text-primary-orange transition-colors duration-200">$</span>
                  <span className="ml-2">{item.name.toLowerCase()}</span>
                  <span className="ml-2 text-xs text-primary-purple opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    [{index + 1}]
                  </span>
                </button>
              ))}
            </div>

            {/* Footer */}
            <div className="mt-12 text-center space-y-2">
              <div className="text-xs text-text-secondary">
                <span className="text-primary-purple">Press ESC or click outside to close</span>
              </div>
              <div className="text-xs text-primary-green font-mono">
                Connection: SECURE | Status: READY
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
